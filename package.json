{"name": "tt-pcp-management-portal", "type": "module", "version": "1.17.0-beta.39", "private": true, "sideEffects": false, "engines": {"node": ">=20.14.0"}, "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "lint-fix": "eslint --fix --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc", "release": "commit-and-tag-version -t", "test": "npx playwright test -- --browser all", "generate-api-service": "npx cross-env NODE_TLS_REJECT_UNAUTHORIZED=0 npx @hey-api/openapi-ts -i https://localhost:7108/swagger/v1/swagger.json -o app/services/api-generated -c @hey-api/client-fetch"}, "dependencies": {"@azure/msal-node": "^2.13.1", "@hey-api/client-fetch": "^0.4.4", "@hookform/resolvers": "^3.9.1", "@netpro/auth": "^1.0.2", "@netpro/design-system": "^3.7.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@remix-run/node": "^2.11.2", "@remix-run/react": "^2.11.2", "@remix-run/serve": "^2.11.2", "@tanstack/react-table": "^8.20.5", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "exceljs": "^4.4.0", "flat": "^6.0.1", "isbot": "^4.1.0", "lucide-react": "^0.439.0", "mime": "^4.0.4", "query-string": "^9.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.53.2", "remix-hook-form": "^5.1.1", "uuid": "^11.0.2", "zod": "^3.23.8"}, "devDependencies": {"@hey-api/openapi-ts": "^0.56.1", "@netpro/eslint-config": "^5.0.0", "@playwright/test": "^1.47.0", "@remix-run/dev": "^2.11.2", "@types/node": "^22.5.4", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "autoprefixer": "^10.4.19", "commit-and-tag-version": "^12.4.2", "eslint": "^9.14.0", "postcss": "^8.4.38", "remix-development-tools": "^4.7.7", "tailwindcss": "^3.4.4", "typescript": "^5.1.6", "vite": "^5.1.0", "vite-tsconfig-paths": "^4.2.1"}, "commit-and-tag-version": {"releaseCommitMessageFormat": "chore(release): v{{currentTag}} see changelog for details [skip ci]", "commitUrlFormat": "https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20Management%20Portal/commit/{{hash}}", "issueUrlFormat": "https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/{{id}}", "compareUrlFormat": "https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/branchCompare?baseVersion=GT{{previousTag}}&targetVersion=GT{{currentTag}}&_a=files"}, "overrides": {"@remix-run/server-runtime": {"cookie": "^0.7.2"}}}